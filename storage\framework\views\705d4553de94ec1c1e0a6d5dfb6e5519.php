<ol>
    <li>
        <p>
            <a
                href="https://razorpay.com"
                target="_blank"
            >
                <?php echo e(__('Register an account on :name', ['name' => 'Razorpay'])); ?>

            </a>
        </p>
    </li>
    <li>
        <p>
            <?php echo e(__('After registration at :name, you will have Client ID, Client Secret', ['name' => 'Razorpay'])); ?>

        </p>
    </li>
    <li>
        <p>
            <?php echo e(__('Enter Client ID, Secret into the box in right hand')); ?>

        </p>
    </li>
    <li>
        <p>
            <?php echo BaseHelper::clean(__('Then you need to create a new webhook. To create a webhook, go to <strong>Account Settings</strong>-><strong>API keys</strong>-><strong>Webhooks</strong> and paste the below url to <strong>Webhook URL</strong> field.')); ?>

        </p>

        <code><?php echo e(route('payments.razorpay.webhook')); ?></code>

        <p class="mt-2">
            <?php echo BaseHelper::clean(__('At <strong>Active Events</strong> field, make sure to enable the following events:')); ?>

        </p>

        <ul class="ps-3 mt-2">
            <li><strong>payment.authorized</strong> - <?php echo e(__('When a payment is authorized')); ?></li>
            <li><strong>payment.captured</strong> - <?php echo e(__('When a payment is captured')); ?></li>
            <li><strong>payment.failed</strong> - <?php echo e(__('When a payment fails')); ?></li>
            <li><strong>payment.pending</strong> - <?php echo e(__('When a payment is pending')); ?></li>
            <li><strong>order.paid</strong> - <?php echo e(__('When an order is paid')); ?></li>
        </ul>

        <p class="mt-2">
            <?php echo BaseHelper::clean(__('It is important to enable <strong>ALL</strong> these events to ensure your system captures all payment statuses correctly. Missing events may result in payments not being recorded in your system.')); ?>

        </p>

        <p class="mt-2">
            <?php echo BaseHelper::clean(__('After creating the webhook, Razorpay will generate a <strong>Webhook Secret</strong>. Copy this secret and paste it into the <strong>Webhook Secret</strong> field in the settings form. This is required for secure webhook verification.')); ?>

        </p>
    </li>
</ol>
<?php /**PATH D:\laragon\www\shofy\platform/plugins/razorpay/resources/views/instructions.blade.php ENDPATH**/ ?>