(()=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,n(r.key),r)}}function n(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,n||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}const i=function(){return e=function e(t,n,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.loader=t,this.url=n,this.t=i},(n=[{key:"upload",value:function(){var e=this;return this.loader.file.then((function(t){return new Promise((function(n,i){e._initRequest(),e._initListeners(n,i,t),e._sendRequest(t)}))}))}},{key:"abort",value:function(){this.xhr&&this.xhr.abort()}},{key:"_initRequest",value:function(){var e=this.xhr=new XMLHttpRequest;e.open("POST",this.url,!0),e.responseType="json"}},{key:"_initListeners",value:function(e,t,n){var i=this.xhr,r=this.loader,o=(0,this.t)("Cannot upload file:")+" ".concat(n.name,".");i.addEventListener("error",(function(){return t(o)})),i.addEventListener("abort",(function(){return t()})),i.addEventListener("load",(function(){var n=i.response;if(!n||!n.uploaded)return t(n&&n.error&&n.error.message?n.error.message:o);e({default:n.url})})),i.upload&&i.upload.addEventListener("progress",(function(e){e.lengthComputable&&(r.uploadTotal=e.total,r.uploaded=e.loaded)}))}},{key:"_sendRequest",value:function(e){var t=new FormData;t.append("upload",e),t.append("_token",$('meta[name="csrf-token"]').attr("content")),this.xhr.send(t)}}])&&t(e.prototype,n),i&&t(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,i}();function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function a(e,t,n){return(t=m(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},i=n.iterator||"@@iterator",r=n.toStringTag||"@@toStringTag";function o(n,i,r,o){var l=i&&i.prototype instanceof u?i:u,s=Object.create(l.prototype);return c(s,"_invoke",function(n,i,r){var o,l,c,u=0,s=r||[],d=!1,f={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return o=t,l=0,c=e,f.n=n,a}};function p(n,i){for(l=n,c=i,t=0;!d&&u&&!r&&t<s.length;t++){var r,o=s[t],p=f.p,m=o[2];n>3?(r=m===i)&&(c=o[(l=o[4])?5:(l=3,3)],o[4]=o[5]=e):o[0]<=p&&((r=n<2&&p<o[1])?(l=0,f.v=i,f.n=o[1]):p<m&&(r=n<3||o[0]>i||i>m)&&(o[4]=n,o[5]=i,f.n=m,l=0))}if(r||n>1)return a;throw d=!0,i}return function(r,s,m){if(u>1)throw TypeError("Generator is already running");for(d&&1===s&&p(s,m),l=s,c=m;(t=l<2?e:c)||!d;){o||(l?l<3?(l>1&&(f.n=-1),p(l,c)):f.n=c:f.v=c);try{if(u=2,o){if(l||(r="next"),t=o[r]){if(!(t=t.call(o,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,l<2&&(l=0)}else 1===l&&(t=o.return)&&t.call(o),l<2&&(c=TypeError("The iterator does not provide a '"+r+"' method"),l=1);o=e}else if((t=(d=f.n<0)?c:n.call(i,f))!==a)break}catch(t){o=e,l=1,c=t}finally{u=1}}return{value:t,done:d}}}(n,r,o),!0),s}var a={};function u(){}function s(){}function d(){}t=Object.getPrototypeOf;var f=[][i]?t(t([][i]())):(c(t={},i,(function(){return this})),t),p=d.prototype=u.prototype=Object.create(f);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,c(e,r,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=d,c(p,"constructor",d),c(d,"constructor",s),s.displayName="GeneratorFunction",c(d,r,"GeneratorFunction"),c(p),c(p,r,"Generator"),c(p,i,(function(){return this})),c(p,"toString",(function(){return"[object Generator]"})),(l=function(){return{w:o,m}})()}function c(e,t,n,i){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}c=function(e,t,n,i){if(t)r?r(e,t,{value:n,enumerable:!i,configurable:!i,writable:!i}):e[t]=n;else{var o=function(t,n){c(e,t,(function(e){return this._invoke(t,n,e)}))};o("next",0),o("throw",1),o("return",2)}},c(e,t,n,i)}function u(e,t,n,i,r,o,a){try{var l=e[o](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(i,r)}function s(e){return function(){var t=this,n=arguments;return new Promise((function(i,r){var o=e.apply(t,n);function a(e){u(o,i,r,a,l,"next",e)}function l(e){u(o,i,r,a,l,"throw",e)}a(void 0)}))}}function d(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return f(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){l=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(l)throw o}}}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function p(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,m(i.key),i)}}function m(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}var h=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.CKEDITOR={},this.ckEditorConfigCallbacks=[],this.ckEditorInitialCallbacks=[],this.ckFinderCallback=null,this.tinyMceConfigCallbacks=[],this.tinyMceInitialCallbacks=[],document.dispatchEvent(new CustomEvent("core-editor-init",{detail:this}))},t=[{key:"ckEditorConfigUsing",value:function(e){this.ckEditorConfigCallbacks.push(e)}},{key:"ckEditorInitialUsing",value:function(e){this.ckEditorInitialCallbacks.push(e)}},{key:"ckEditorConfig",value:function(e){var t,n=d(this.ckEditorConfigCallbacks);try{for(n.s();!(t=n.n()).done;)e=(0,t.value)(e)}catch(e){n.e(e)}finally{n.f()}return e}},{key:"ckFinderUsing",value:function(e){this.ckFinderCallback=e}},{key:"ckFinderInitial",value:(u=s(l().m((function e(t,n){var r,o,a;return l().w((function(e){for(;;)switch(e.n){case 0:if(!this.ckFinderCallback){e.n=1;break}return e.a(2,this.ckFinderCallback(t,n));case 1:(r=t.plugins.get("FileRepository"))&&"undefined"!=typeof RV_MEDIA_URL&&RV_MEDIA_URL.media_upload_from_editor&&(r.createUploadAdapter=function(e){return new i(e,RV_MEDIA_URL.media_upload_from_editor,t.t)}),o=t.commands.get("ckfinder"),a=$("#".concat(n)).parent().find('.btn_gallery[data-action="media-insert-ckeditor"]'),o&&a.length?o.execute=function(){return a.trigger("click")}:o.execute=function(){return Botble.showError("Not available.")};case 2:return e.a(2)}}),e,this)}))),function(e,t){return u.apply(this,arguments)})},{key:"initCkEditor",value:function(e,t){var n=this;if(this.CKEDITOR[e]||!$("#"+e).is(":visible"))return!1;var i=document.querySelector("#"+e),r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){a(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({fontSize:{options:[9,10,11,12,13,"default",15,16,17,18,19,20,21,22,23,24]},alignment:{options:["left","right","center","justify"]},heading:{options:[{model:"paragraph",title:"Paragraph",class:"ck-heading_paragraph"},{model:"heading1",view:"h1",title:"Heading 1",class:"ck-heading_heading1"},{model:"heading2",view:"h2",title:"Heading 2",class:"ck-heading_heading2"},{model:"heading3",view:"h3",title:"Heading 3",class:"ck-heading_heading3"},{model:"heading4",view:"h4",title:"Heading 4",class:"ck-heading_heading4"},{model:"heading5",view:"h5",title:"Heading 5",class:"ck-heading_heading4"},{model:"heading6",view:"h6",title:"Heading 6",class:"ck-heading_heading4"}]},placeholder:" ",toolbar:{items:["heading","|","fontColor","fontSize","fontBackgroundColor","fontFamily","bold","italic","underline","link","strikethrough","bulletedList","numberedList","|","alignment","direction","shortcode","outdent","indent","|","htmlEmbed","imageInsert","ckfinder","blockQuote","insertTable","mediaEmbed","bootstrapGrid","undo","redo","findAndReplace","removeFormat","sourceEditing","codeBlock","fullScreen"],shouldNotGroupWhenFull:!0},language:{ui:window.siteEditorLocale||"en",content:window.siteEditorLocale||"en"},image:{toolbar:["imageTextAlternative","imageStyle:inline","imageStyle:block","imageStyle:side","imageStyle:wrapText","imageStyle:breakText","toggleImageCaption","ImageResize"],upload:{types:["jpeg","png","gif","bmp","webp","tiff","svg+xml"]}},codeBlock:{languages:[{language:"plaintext",label:"Plain text"},{language:"c",label:"C"},{language:"cs",label:"C#"},{language:"cpp",label:"C++"},{language:"css",label:"CSS"},{language:"diff",label:"Diff"},{language:"html",label:"HTML"},{language:"java",label:"Java"},{language:"javascript",label:"JavaScript"},{language:"php",label:"PHP"},{language:"python",label:"Python"},{language:"ruby",label:"Ruby"},{language:"typescript",label:"TypeScript"},{language:"xml",label:"XML"},{language:"dart",label:"Dart",class:"language-dart"}]},link:{defaultProtocol:"http://",decorators:{openInNewTab:{mode:"manual",label:"Open in a new tab",attributes:{target:"_blank",rel:"noopener noreferrer"}}}},table:{contentToolbar:["tableColumn","tableRow","mergeTableCells","tableCellProperties","tableProperties"]},htmlSupport:{allow:[{name:/.*/,attributes:!0,classes:!0,styles:!0}]},mediaEmbed:{extraProviders:[{name:"tiktok",url:"^.*https:\\/\\/(?:m|www|vm)?\\.?tiktok\\.com\\/((?:.*\\b(?:(?:usr|v|embed|user|video)\\/|\\?shareId=|\\&item_id=)(\\d+))|\\w+)",html:function(e){return'<iframe src="https://www.tiktok.com/embed/v2/'.concat(e[1],'" width="100%" height="400" frameborder="0"></iframe>')}}]}},t);r=this.ckEditorConfig(r),ClassicEditor.create(i,r).then(function(){var t=s(l().m((function t(i){var r,o,a;return l().w((function(t){for(;;)switch(t.n){case 0:return i.insertHtml=function(e){var t=i.data.processor.toView(e),n=i.data.toModel(t);i.model.insertContent(n)},window.editor=i,n.CKEDITOR[e]=i,r=90*$("#"+e).prop("rows"),o="ckeditor-".concat(e,"-inline"),$(i.ui.view.editable.element).addClass(o).after("\n                    <style>\n                        .ck-editor__editable_inline {\n                            min-height: ".concat(r-100,"px;\n                            max-height: ").concat(r+100,"px;\n                        }\n                    </style>\n                ")),i.model.document.on("change:data",(function(){clearTimeout(a),a=setTimeout((function(){i.updateSourceElement()}),150)})),i.commands._commands.get("mediaEmbed").execute=function(e){i.execute("shortcode",'[media url="'.concat(e,'"][/media]'))},t.n=1,n.ckEditorInitialUsing(i);case 1:return t.n=2,n.ckFinderInitial(i,e);case 2:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(e){console.error(e)}))}},{key:"uploadImageFromEditor",value:function(e,t){var n=new FormData;"function"==typeof e.blob?n.append("upload",e.blob(),e.filename()):n.append("upload",e),$httpClient.make().postForm(RV_MEDIA_URL.media_upload_from_editor,n).then((function(e){var n=e.data;n.uploaded&&t(n.url)}))}},{key:"tinyMceConfigUsing",value:function(e){this.tinyMceConfigCallbacks.push(e)}},{key:"tinyMceInitialUsing",value:function(e){this.tinyMceInitialCallbacks.push(e)}},{key:"tinyMceConfig",value:function(e){var t,n=d(this.tinyMceConfigCallbacks);try{for(n.s();!(t=n.n()).done;)e=(0,t.value)(e)}catch(e){n.e(e)}finally{n.f()}return e}},{key:"tinyMceInitial",value:(c=s(l().m((function e(t){return l().w((function(e){for(;;)if(0===e.n)return e.a(2,t)}),e)}))),function(e){return c.apply(this,arguments)})},{key:"initTinyMce",value:(r=s(l().m((function e(t){var n,i,r=this;return l().w((function(e){for(;;)switch(e.n){case 0:return n={menubar:!0,selector:"#".concat(t),min_height:110*$("#".concat(t)).prop("rows"),resize:"vertical",plugins:"code autolink advlist visualchars link image media table charmap hr pagebreak nonbreaking anchor insertdatetime lists wordcount imagetools visualblocks",extended_valid_elements:"input[id|name|value|type|class|style|required|placeholder|autocomplete|onclick]",toolbar:"formatselect | bold italic strikethrough forecolor backcolor | link image table | alignleft aligncenter alignright alignjustify  | numlist bullist indent  |  visualblocks code",convert_urls:!1,image_caption:!0,image_advtab:!0,image_title:!0,placeholder:"",contextmenu:"link image inserttable | cell row column deletetable",images_upload_url:RV_MEDIA_URL.media_upload_from_editor,automatic_uploads:!0,block_unsupported_drop:!1,file_picker_types:"file image media",images_upload_handler:this.uploadImageFromEditor.bind(this),file_picker_callback:function(e){$('<input type="file" accept="image/*" />').click().on("change",(function(t){r.uploadImageFromEditor(t.target.files[0],e)}))},directionality:$("body").prop("dir")||"ltr"},"dark"===localStorage.getItem("themeMode")&&(n.skin="oxide-dark",n.content_css="dark"),n=this.tinyMceConfig(n),i=tinymce.init(n),e.n=1,this.tinyMceInitial(i);case 1:return e.a(2)}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"initEditor",value:function(e,t,n){if(!e.length)return!1;var i=this;switch(n){case"ckeditor":$.each(e,(function(e,n){i.initCkEditor($(n).prop("id"),t)}));break;case"tinymce":$.each(e,(function(e,t){i.initTinyMce($(t).prop("id"))}))}}},{key:"init",value:function(){var e=this,t=$(document).find(".editor-ckeditor"),n=$(document).find(".editor-tinymce"),i=this;return t.length>0&&i.initEditor(t,{},"ckeditor"),n.length>0&&i.initEditor(n,{},"tinymce"),$(document).off("click",".show-hide-editor-btn").on("click",".show-hide-editor-btn",(function(t){t.preventDefault();var n=$(t.currentTarget).data("result"),r=$("#"+n);if(r.hasClass("editor-ckeditor")){var o=$(".editor-action-item");e.CKEDITOR[n]&&void 0!==e.CKEDITOR[n]?(e.CKEDITOR[n].destroy(),e.CKEDITOR[n]=null,o.not(".action-show-hide-editor").hide()):(i.initCkEditor(n,{},"ckeditor"),o.not(".action-show-hide-editor").show())}else r.hasClass("editor-tinymce")&&tinymce.execCommand("mceToggleEditor",!1,n)})),this}}],t&&p(e.prototype,t),n&&p(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,c,u}();$((function(){window.EDITOR=(new h).init(),window.EditorManagement=window.EditorManagement||h,$(document).on("shown.bs.modal",(function(){window.EDITOR.init()}))}))})();