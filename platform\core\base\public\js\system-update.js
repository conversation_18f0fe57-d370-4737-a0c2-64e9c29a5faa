(()=>{"use strict";var e={27287:(e,t,n)=>{n.d(t,{A:()=>a});var r=n(76798),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,'.half-circle-spinner,.half-circle-spinner *{box-sizing:border-box}.half-circle-spinner{border-radius:100%;height:60px;position:relative;width:60px}.half-circle-spinner .circle{border:6px solid transparent;border-radius:100%;content:"";height:100%;position:absolute;width:100%}.half-circle-spinner .circle.circle-1{animation:half-circle-spinner-animation 1s infinite;border-top-color:#ff1d5e}.half-circle-spinner .circle.circle-2{animation:half-circle-spinner-animation 1s infinite alternate;border-bottom-color:#ff1d5e}@keyframes half-circle-spinner-animation{0%{transform:rotate(0)}to{transform:rotate(1turn)}}',""]);const a=o},49433:(e,t,n)=>{n.d(t,{A:()=>a});var r=n(76798),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".updating[data-v-49e58a73]{-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);background:rgba(0,0,0,.6);height:100%;left:0;overflow:hidden;position:fixed;top:0;width:100%;z-index:9999}.updating>.updating-wrapper[data-v-49e58a73]{height:100%;position:absolute;top:calc(30% - 100px);width:100%}.updating>.updating-wrapper>.updating-container[data-v-49e58a73]{margin:0 auto;max-width:500px;text-align:center}.updating>.updating-wrapper>.updating-container .loader[data-v-49e58a73]{margin:0 auto}.updating>.updating-wrapper>.updating-container .loader[data-v-49e58a73]:after{display:none}.updating>.updating-wrapper>.updating-container .percent[data-v-49e58a73]{color:#fefefe;font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-size:86px;margin-bottom:24px}.updating>.updating-wrapper>.updating-container .information[data-v-49e58a73]{color:#efefef;font-size:18px;margin:32px 0;padding:0 8px}.updating>.updating-wrapper>.updating-container .important[data-v-49e58a73]{color:#efefef}.updating>.updating-wrapper>.updating-container .loader .half-circle-spinner[data-v-49e58a73]{margin:0 auto 20px}.updating .red[data-v-49e58a73]{color:#fdc9c9}.updating .red-shadow[data-v-49e58a73]{text-shadow:0 0 16px #ef0012}",""]);const a=o},66262:(e,t)=>{t.A=(e,t)=>{const n=e.__vccOpts||e;for(const[e,r]of t)n[e]=r;return n}},76798:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,r){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(r)for(var a=0;a<this.length;a++){var i=this[a][0];null!=i&&(o[i]=!0)}for(var c=0;c<e.length;c++){var l=[].concat(e[c]);r&&o[l[0]]||(n&&(l[2]?l[2]="".concat(n," and ").concat(l[2]):l[2]=n),t.push(l))}},t}},85072:(e,t,n)=>{var r,o=function(){return void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r},a=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}e[t]=n}return e[t]}}(),i=[];function c(e){for(var t=-1,n=0;n<i.length;n++)if(i[n].identifier===e){t=n;break}return t}function l(e,t){for(var n={},r=[],o=0;o<e.length;o++){var a=e[o],l=t.base?a[0]+t.base:a[0],s=n[l]||0,d="".concat(l," ").concat(s);n[l]=s+1;var p=c(d),u={css:a[1],media:a[2],sourceMap:a[3]};-1!==p?(i[p].references++,i[p].updater(u)):i.push({identifier:d,updater:v(u,t),references:1}),r.push(d)}return r}function s(e){var t=document.createElement("style"),r=e.attributes||{};if(void 0===r.nonce){var o=n.nc;o&&(r.nonce=o)}if(Object.keys(r).forEach((function(e){t.setAttribute(e,r[e])})),"function"==typeof e.insert)e.insert(t);else{var i=a(e.insert||"head");if(!i)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");i.appendChild(t)}return t}var d,p=(d=[],function(e,t){return d[e]=t,d.filter(Boolean).join("\n")});function u(e,t,n,r){var o=n?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(e.styleSheet)e.styleSheet.cssText=p(t,o);else{var a=document.createTextNode(o),i=e.childNodes;i[t]&&e.removeChild(i[t]),i.length?e.insertBefore(a,i[t]):e.appendChild(a)}}function f(e,t,n){var r=n.css,o=n.media,a=n.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),a&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var m=null,h=0;function v(e,t){var n,r,o;if(t.singleton){var a=h++;n=m||(m=s(t)),r=u.bind(null,n,a,!1),o=u.bind(null,n,a,!0)}else n=s(t),r=f.bind(null,n,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=o());var n=l(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var r=0;r<n.length;r++){var o=c(n[r]);i[o].references--}for(var a=l(e,t),s=0;s<n.length;s++){var d=c(n[s]);0===i[d].references&&(i[d].updater(),i.splice(d,1))}n=a}}}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={id:r,exports:{}};return e[r](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0;const r=Vue;var o={class:"content"},a={key:0},i={key:0,xmlns:"http://www.w3.org/2000/svg",class:"icon ms-1",width:"24",height:"24",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},c={key:1,xmlns:"http://www.w3.org/2000/svg",class:"icon ms-1",width:"24",height:"24",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},l={key:2},s={key:3},d={key:1,class:"updating"},p={class:"updating-wrapper"},u={class:"updating-container"},f={key:0,class:"loader"},m=["textContent"],h={class:"information"},v=["textContent"],g={key:1,class:"important red-shadow"},y={key:2};var k=n(85072),b=n.n(k),w=n(27287),x={insert:"head",singleton:!1};b()(w.A,x);w.A.locals;function S(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function a(n,r,o,a){var l=r&&r.prototype instanceof c?r:c,s=Object.create(l.prototype);return E(s,"_invoke",function(n,r,o){var a,c,l,s=0,d=o||[],p=!1,u={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,n){return a=t,c=0,l=e,u.n=n,i}};function f(n,r){for(c=n,l=r,t=0;!p&&s&&!o&&t<d.length;t++){var o,a=d[t],f=u.p,m=a[2];n>3?(o=m===r)&&(l=a[(c=a[4])?5:(c=3,3)],a[4]=a[5]=e):a[0]<=f&&((o=n<2&&f<a[1])?(c=0,u.v=r,u.n=a[1]):f<m&&(o=n<3||a[0]>r||r>m)&&(a[4]=n,a[5]=r,u.n=m,c=0))}if(o||n>1)return i;throw p=!0,r}return function(o,d,m){if(s>1)throw TypeError("Generator is already running");for(p&&1===d&&f(d,m),c=d,l=m;(t=c<2?e:l)||!p;){a||(c?c<3?(c>1&&(u.n=-1),f(c,l)):u.n=l:u.v=l);try{if(s=2,a){if(c||(o="next"),t=a[o]){if(!(t=t.call(a,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,c<2&&(c=0)}else 1===c&&(t=a.return)&&t.call(a),c<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),c=1);a=e}else if((t=(p=u.n<0)?l:n.call(r,u))!==i)break}catch(t){a=e,c=1,l=t}finally{s=1}}return{value:t,done:p}}}(n,o,a),!0),s}var i={};function c(){}function l(){}function s(){}t=Object.getPrototypeOf;var d=[][r]?t(t([][r]())):(E(t={},r,(function(){return this})),t),p=s.prototype=c.prototype=Object.create(d);function u(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,s):(e.__proto__=s,E(e,o,"GeneratorFunction")),e.prototype=Object.create(p),e}return l.prototype=s,E(p,"constructor",s),E(s,"constructor",l),l.displayName="GeneratorFunction",E(s,o,"GeneratorFunction"),E(p),E(p,o,"Generator"),E(p,r,(function(){return this})),E(p,"toString",(function(){return"[object Generator]"})),(S=function(){return{w:a,m:u}})()}function E(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}E=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var a=function(t,n){E(e,t,(function(e){return this._invoke(t,n,e)}))};a("next",0),a("throw",1),a("return",2)}},E(e,t,n,r)}function B(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(r,o)}function C(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){B(a,r,o,i,c,"next",e)}function c(e){B(a,r,o,i,c,"throw",e)}i(void 0)}))}}const N={components:{HalfCircleSpinner:((e,t)=>{const n=e.__vccOpts||e;for(const[e,r]of t)n[e]=r;return n})({name:"HalfCircleSpinner",props:{animationDuration:{type:Number,default:1e3},size:{type:Number,default:60},color:{type:String,default:"#fff"}},computed:{spinnerStyle(){return{height:`${this.size}px`,width:`${this.size}px`}},circleStyle(){return{borderWidth:this.size/10+"px",animationDuration:`${this.animationDuration}ms`}},circle1Style(){return Object.assign({borderTopColor:this.color},this.circleStyle)},circle2Style(){return Object.assign({borderBottomColor:this.color},this.circleStyle)}}},[["render",function(e,t,n,o,a,i){return(0,r.openBlock)(),(0,r.createElementBlock)("div",{class:"half-circle-spinner",style:(0,r.normalizeStyle)(i.spinnerStyle)},[(0,r.createElementVNode)("div",{class:"circle circle-1",style:(0,r.normalizeStyle)(i.circle1Style)},null,4),(0,r.createElementVNode)("div",{class:"circle circle-2",style:(0,r.normalizeStyle)(i.circle2Style)},null,4)],4)}]])},props:{updateUrl:String,updateId:String,version:String,firstStep:String,firstStepMessage:String,lastStep:String,isOutdated:Boolean,isActivated:Boolean},data:function(){return{askToProcessUpdate:!1,performingUpdate:!1,results:[],realPercent:0,percent:0,percentInterval:0,step:this.firstStep,loading:!1}},watch:{realPercent:function(){var e=this;this.percentInterval||(this.percentInterval=setInterval((function(){e.percent>=e.realPercent||(100!==e.percent?e.percent+=1:clearInterval(e.percentInterval))}),100))}},methods:{triggerAskToProcessUpdate:function(){this.isActivated?this.askToProcessUpdate=!0:$("#system-updater-confirm-modal").modal("show")},performUpdate:function(){var e=this;return C(S().m((function t(){return S().w((function(t){for(;;)switch(t.n){case 0:return e.loading=!0,e.performingUpdate=!0,e.realPercent=5,e.results.push({text:e.firstStepMessage,error:!1}),t.p=1,t.n=2,e.triggerUpdate();case 2:setTimeout((function(){return e.refresh()}),3e4),t.n=4;break;case 3:t.p=3,t.v,e.loading=!1;case 4:return t.a(2)}}),t,null,[[1,3]])})))()},triggerUpdate:function(){var e=this;return C(S().m((function t(){return S().w((function(t){for(;;)if(0===t.n)return t.a(2,e.$httpClient.makeWithoutErrorHandler().post(e.updateUrl,{step_name:e.step,update_id:e.updateId,version:e.version}).then((function(t){var n=t.data;if(!n.data||!n.data.next_step||!n.data.next_message)throw new Error("Something went wrong, could not update the system.");if(e.step=n.data.next_step,e.realPercent=n.data.current_percent,e.results.push({text:n.data.next_message,error:!1}),n.data.next_step!==e.lastStep)return e.triggerUpdate();e.percent=100,e.loading=!1,clearInterval(e.percentInterval)})).catch((function(t){var n=t.message;throw e.loading=!1,t.data&&t.data.message?n=t.data.message:t.response&&t.response.data.message&&(n=t.response.data.message),e.results.push({text:n,error:!0}),t})))}),t)})))()},refresh:function(){window.location.reload()}}};var T=n(49433),O={insert:"head",singleton:!1};b()(T.A,O);T.A.locals;const V=(0,n(66262).A)(N,[["render",function(e,t,n,k,b,w){var x=(0,r.resolveComponent)("half-circle-spinner");return(0,r.openBlock)(),(0,r.createElementBlock)("div",o,[(0,r.renderSlot)(e.$slots,"default",(0,r.normalizeProps)((0,r.guardReactiveProps)({performUpdate:w.performUpdate})),void 0,!0),b.performingUpdate?(0,r.createCommentVNode)("",!0):((0,r.openBlock)(),(0,r.createElementBlock)("div",a,[b.askToProcessUpdate?(0,r.createCommentVNode)("",!0):((0,r.openBlock)(),(0,r.createElementBlock)("button",{key:0,type:"button",class:"btn btn-warning",onClick:t[0]||(t[0]=(0,r.withModifiers)((function(){return w.triggerAskToProcessUpdate&&w.triggerAskToProcessUpdate.apply(w,arguments)}),["prevent"]))},[n.isOutdated?((0,r.openBlock)(),(0,r.createElementBlock)("svg",c,t[4]||(t[4]=[(0,r.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"},null,-1),(0,r.createElementVNode)("path",{d:"M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"},null,-1),(0,r.createElementVNode)("path",{d:"M7 11l5 5l5 -5"},null,-1),(0,r.createElementVNode)("path",{d:"M12 4l0 12"},null,-1)]))):((0,r.openBlock)(),(0,r.createElementBlock)("svg",i,t[3]||(t[3]=[(0,r.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"},null,-1),(0,r.createElementVNode)("path",{d:"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"},null,-1),(0,r.createElementVNode)("path",{d:"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"},null,-1)]))),n.isOutdated?((0,r.openBlock)(),(0,r.createElementBlock)("span",l,"Download & Install Update")):((0,r.openBlock)(),(0,r.createElementBlock)("span",s,"Re-install The Latest Version"))])),b.askToProcessUpdate?((0,r.openBlock)(),(0,r.createElementBlock)("button",{key:1,type:"button",class:"btn btn-danger",onClick:t[1]||(t[1]=function(){return w.performUpdate&&w.performUpdate.apply(w,arguments)})},t[5]||(t[5]=[(0,r.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",class:"icon ms-1",width:"24",height:"24",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},[(0,r.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,r.createElementVNode)("path",{d:"M5 12l5 5l10 -10"})],-1),(0,r.createTextVNode)(" Click To Confirm! ")]))):(0,r.createCommentVNode)("",!0)])),b.performingUpdate?((0,r.openBlock)(),(0,r.createElementBlock)("div",d,[(0,r.createElementVNode)("div",p,[(0,r.createElementVNode)("div",u,[b.loading?((0,r.openBlock)(),(0,r.createElementBlock)("div",f,[(0,r.createVNode)(x,{"animation-duration":1e3,size:32})])):(0,r.createCommentVNode)("",!0),(0,r.createElementVNode)("div",{class:"percent",textContent:(0,r.toDisplayString)("".concat(b.percent,"%"))},null,8,m),(0,r.createElementVNode)("div",h,[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(b.results,(function(e){return(0,r.openBlock)(),(0,r.createElementBlock)("p",{textContent:(0,r.toDisplayString)(e.text),class:(0,r.normalizeClass)(e.error?"bold text-pink red-shadow":"bold")},null,10,v)})),256))]),b.loading?((0,r.openBlock)(),(0,r.createElementBlock)("div",g,t[6]||(t[6]=[(0,r.createElementVNode)("strong",null,"DO NOT CLOSE WINDOWS DURING UPDATE!",-1)]))):((0,r.openBlock)(),(0,r.createElementBlock)("div",y,[(0,r.createElementVNode)("div",{class:"btn btn-info",onClick:t[2]||(t[2]=function(){return w.refresh&&w.refresh.apply(w,arguments)})},"Click Here To Exit")]))])])])):(0,r.createCommentVNode)("",!0)])}],["__scopeId","data-v-49e58a73"]]);"undefined"!=typeof vueApp&&vueApp.booting((function(e){e.component("system-update-component",V)}))})();