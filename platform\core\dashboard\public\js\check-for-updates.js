(()=>{"use strict";var e={66262:(e,t)=>{t.A=(e,t)=>{const s=e.__vccOpts||e;for(const[e,a]of t)s[e]=a;return s}}},t={};const s=Vue;const a={props:{checkUpdateUrl:{type:String,default:function(){return null},required:!0}},data:function(){return{hasNewVersion:!1,message:null}},mounted:function(){this.checkUpdate()},methods:{checkUpdate:function(){var e=this,t=function(){var e=localStorage.getItem("system_update_check_time");if(!e)return!0;return Date.now()-parseInt(e)>9e5},s="true"===localStorage.getItem("system_update_has_new_version"),a=localStorage.getItem("system_update_message");if(s&&a&&!t())return this.hasNewVersion=s,void(this.message=a);t()&&axios.get(this.checkUpdateUrl).then((function(t){var s=t.data;localStorage.setItem("system_update_check_time",Date.now().toString()),!s.error&&s.data.has_new_version?(e.hasNewVersion=!0,e.message=s.message,localStorage.setItem("system_update_has_new_version","true"),localStorage.setItem("system_update_message",s.message)):(localStorage.setItem("system_update_has_new_version","false"),localStorage.removeItem("system_update_message"))})).catch((function(){localStorage.setItem("system_update_check_time",Date.now().toString())}))}}};var r=function s(a){var r=t[a];if(void 0!==r)return r.exports;var o=t[a]={exports:{}};return e[a](o,o.exports,s),o.exports}(66262);const o=(0,r.A)(a,[["render",function(e,t,a,r,o,n){return(0,s.renderSlot)(e.$slots,"default",(0,s.normalizeProps)((0,s.guardReactiveProps)({hasNewVersion:o.hasNewVersion,message:o.message})))}]]);"undefined"!=typeof vueApp&&vueApp.booting((function(e){e.component("v-check-for-updates",o)}))})();