<?php

namespace Bo<PERSON><PERSON>\FiatFlo\Forms;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Payment\Concerns\Forms\HasAvailableCountriesField;
use Botble\Payment\Forms\PaymentMethodForm;

class FiatFloPaymentMethodForm extends PaymentMethodForm
{
    use HasAvailableCountriesField;

    public function setup(): void
    {
        parent::setup();

        $this
            ->paymentId(FIATFLO_PAYMENT_METHOD_NAME)
            ->paymentName('FiatFlo')
            ->paymentDescription(__('Customer can buy product and pay directly using Visa, Credit card via :name', ['name' => 'FiatFlo']))
            ->paymentLogo(url('vendor/core/plugins/fiatflo/images/fiatflo.png'))
            ->paymentFeeField(FIATFLO_PAYMENT_METHOD_NAME)
            ->paymentUrl('https://fiatflo.com')
            ->paymentInstructions(view('plugins/fiatflo::instructions')->render())
            ->add(
                sprintf('payment_%s_api_key', FIATFLO_PAYMENT_METHOD_NAME),
                TextField::class,
                TextFieldOption::make()
                    ->label(__('API Key'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('api_key', FIATFLO_PAYMENT_METHOD_NAME))
                    ->placeholder(__('Enter your FiatFlo API Key'))
                    ->attributes(['data-counter' => 400])
            )
            ->add(
                sprintf('payment_%s_method_type', FIATFLO_PAYMENT_METHOD_NAME),
                SelectField::class,
                SelectFieldOption::make()
                    ->label(__('Card Type'))
                    ->choices([
                        'card' => 'Card',
                        'india' => 'UPI - Indian Payment Method'
                    ])
                    ->selected(get_payment_setting('method_type', FIATFLO_PAYMENT_METHOD_NAME, 'card'))
            )
            ->addAvailableCountriesField(FIATFLO_PAYMENT_METHOD_NAME);
    }
}
