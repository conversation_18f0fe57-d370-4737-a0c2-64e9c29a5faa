@if (setting('payment_fiatflo_status') == 1)
    <x-plugins-payment::payment-method
        :name="FIATFLO_PAYMENT_METHOD_NAME"
        paymentName="FiatFlo"
        :supportedCurrencies="(new Botble\FiatFlo\Services\Gateways\FiatFloPaymentService)->supportedCurrencyCodes()"
    >
        <div class="fiatflo-payment-info">
            <p class="text-muted">{{ __('You will be redirected to FiatFlo to complete your payment securely.') }}</p>
            <div class="payment-card-types mt-2">
                <small class="text-muted">
                    {{ __('Accepted card type: :type', ['type' => ucfirst(get_payment_setting('card_type', FIATFLO_PAYMENT_METHOD_NAME, 'visa'))]) }}
                </small>
            </div>
        </div>
    </x-plugins-payment::payment-method>
@endif
