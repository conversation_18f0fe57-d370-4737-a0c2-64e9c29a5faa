<?php

namespace Botble\FiatFlo\Services\Gateways;

use Botble\FiatFlo\Services\Abstracts\FiatFloPaymentAbstract;
use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class FiatFloPaymentService extends FiatFloPaymentAbstract
{
    protected string $baseUrl = 'https://app.fiatflo.com/api';

    public function makePayment(array $data): bool|string
    {
        try {
            // Validate required data
            if (empty($this->apiKey)) {
                $this->setErrorMessage('FiatFlo API key is not configured');
                return false;
            }

            $paymentData = [
                'amount' => $this->amount,
                'currency' => $this->currency,
                'method_type' => $this->methodType,
                'reference' => $data['checkout_token'] ?? Str::random(20),
                'description' => $data['description'] ?? 'Payment for order #' . implode(', #', (array) ($data['order_id'] ?? [])),
                'return_url' => PaymentHelper::getRedirectURL($data['checkout_token']),
                'cancel_url' => PaymentHelper::getCancelURL($data['checkout_token']),
                'webhook_url' => route('fiatflo.webhook'),
                'metadata' => [
                    'order_id' => $data['order_id'] ?? [],
                    'customer_id' => $data['customer_id'] ?? null,
                    'customer_type' => $data['customer_type'] ?? null,
                    'checkout_token' => $data['checkout_token'] ?? null,
                ],
            ];

            do_action('payment_before_making_api_request', FIATFLO_PAYMENT_METHOD_NAME, $paymentData);

            $response = Http::timeout(30)->withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'Botble-FiatFlo-Plugin/1.0.0',
            ])->post($this->baseUrl . '/payments', $paymentData);

            $responseData = $response->json() ?? [];

            do_action('payment_after_api_response', FIATFLO_PAYMENT_METHOD_NAME, $paymentData, $responseData);

            if ($response->successful()) {
                if (isset($responseData['payment_url']) && filter_var($responseData['payment_url'], FILTER_VALIDATE_URL)) {
                    $this->chargeId = $responseData['id'] ?? $responseData['payment_id'] ?? Str::random(20);

                    return $responseData['payment_url'];
                } else {
                    $this->setErrorMessage('Invalid payment URL received from FiatFlo');
                    return false;
                }
            }

            // Handle API errors
            $errorMessage = $responseData['message'] ?? $responseData['error'] ?? 'Failed to create payment link';
            if ($response->status() === 401) {
                $errorMessage = 'Invalid API key. Please check your FiatFlo configuration.';
            } elseif ($response->status() === 422) {
                $errorMessage = 'Invalid payment data: ' . $errorMessage;
            }

            $this->setErrorMessage($errorMessage);

            return false;
        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 2);

            return false;
        }
    }

    public function afterMakePayment(array $data): void
    {
        PaymentHelper::log(
            FIATFLO_PAYMENT_METHOD_NAME,
            [
                'message' => 'Payment link created successfully',
                'charge_id' => $this->chargeId,
                'amount' => $this->amount,
                'currency' => $this->currency,
            ]
        );
    }

    public function isValidToProcessCheckout(): bool
    {
        return apply_filters('fiatflo_is_valid_to_process_checkout', true);
    }

    public function getOrderNotes(): array
    {
        return apply_filters('fiatflo_order_notes', []);
    }
}
