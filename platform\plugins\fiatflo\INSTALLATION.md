# FiatFlo Payment Gateway - Installation Guide

## Quick Installation Steps

### 1. Plugin Activation
1. Navigate to your admin panel
2. Go to **Plugins** section
3. Find **FiatFlo Payment Gateway** in the list
4. Click **Activate**

### 2. Payment Method Configuration
1. Go to **Settings** > **Payment Methods**
2. Find the **FiatFlo** section
3. Configure the following settings:

#### Required Settings:
- **Status**: Enable the payment method
- **API Key**: Enter your FiatFlo API key
- **Card Type**: Select preferred card type (Visa, Mastercard, Amex, Discover, or All)

#### Optional Settings:
- **Payment Method Name**: Customize the display name (default: "FiatFlo")
- **Description**: Add custom description for customers
- **Payment Fee**: Set additional fees if needed
- **Available Countries**: Restrict to specific countries if needed

### 3. FiatFlo Dashboard Setup
1. Log into your FiatFlo dashboard
2. Navigate to **API Settings** or **Webhooks**
3. Add the webhook URL: `https://yourdomain.com/payment/fiatflo/webhook`
4. Copy your API key and paste it in the plugin settings

### 4. Testing
1. Create a test order on your website
2. Select FiatFlo as payment method
3. Complete a small test transaction
4. Verify the payment status updates correctly

## Logo Setup (Optional)
Add your FiatFlo logo:
1. Place a PNG image at: `platform/plugins/fiatflo/public/images/fiatflo.png`
2. Recommended size: 200x60 pixels
3. The logo will appear in the payment method selection

## Troubleshooting

### Common Issues:

**"API key is not configured"**
- Ensure you've entered the API key in Settings > Payment Methods
- Verify the API key is correct in your FiatFlo dashboard

**"Payment link not created"**
- Check your internet connection
- Verify FiatFlo API is accessible
- Check the payment logs for detailed error messages

**"Webhook not received"**
- Ensure the webhook URL is correctly configured in FiatFlo
- Check if your server can receive POST requests
- Verify SSL certificate if using HTTPS

### Getting Help:
1. Check payment logs in admin panel
2. Review the plugin README.md file
3. Contact FiatFlo support for API-related issues

## Security Notes
- Always use HTTPS for webhook URLs
- Keep your API key secure and never share it
- Regularly monitor payment logs for suspicious activity
- Test thoroughly before going live

## Next Steps
After successful installation:
1. Configure other payment methods if needed
2. Set up email notifications for payments
3. Customize the checkout experience
4. Monitor payment analytics in your dashboard
