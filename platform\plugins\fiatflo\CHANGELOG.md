# Changelog

All notable changes to the FiatFlo Payment Gateway plugin will be documented in this file.

## [1.0.0] - 2025-01-20

### Added
- Initial release of FiatFlo Payment Gateway plugin
- Payment link creation via FiatFlo API
- Webhook support for payment notifications
- Configurable API key and card type settings
- Multi-currency support (USD, EUR, GBP, CAD, AUD, JPY, CHF, CNY, SEK, NZD)
- Payment method form with validation
- Error handling and logging
- Admin panel integration
- Payment status mapping (completed, pending, failed, refunded)
- Secure API communication with timeout handling
- Comprehensive documentation and installation guide

### Features
- Simple and lightweight integration
- Follows Botble CMS payment gateway patterns
- Automatic payment status updates via webhooks
- Configurable card type restrictions
- Real-time payment processing
- Detailed error messages and logging
- Admin-friendly configuration interface

### Security
- Secure API key handling
- Webhook signature validation ready
- HTTPS support for all communications
- Input validation and sanitization

### Documentation
- Complete README with setup instructions
- Installation guide with troubleshooting
- API integration documentation
- Webhook configuration guide
