# FiatFlo Payment Gateway Plugin

This plugin integrates FiatFlo payment gateway with your Botble CMS application.

## Features

- Simple and lightweight payment gateway integration
- Secure payment processing through FiatFlo
- Webhook support for real-time payment notifications
- Configurable card types (Visa, Mastercard, Amex, Discover, or All)
- Multi-currency support
- Easy setup and configuration

## Installation

1. Copy the `fiatflo` folder to your `platform/plugins/` directory
2. Add the FiatFlo logo image as `platform/plugins/fiatflo/public/images/fiatflo.png`
3. Activate the plugin in your admin panel under Plugins
4. Configure the payment method in Settings > Payment Methods

## Configuration

### Required Settings

1. **API Key**: Your FiatFlo API key (obtain from your FiatFlo dashboard)
2. **Card Type**: Select the type of cards to accept (Visa, Mastercard, Amex, Discover, or All)

### Webhook Setup

Configure the following webhook URL in your FiatFlo dashboard:
```
https://yourdomain.com/payment/fiatflo/webhook
```

## API Integration

The plugin creates payment links using the FiatFlo API with the following endpoint:
- **POST** `/api/payments` - Create payment link

### Expected API Response Format

```json
{
    "id": "payment_id",
    "payment_url": "https://fiatflo.com/pay/...",
    "status": "pending"
}
```

### Webhook Payload Format

The plugin expects webhooks with the following format:
```json
{
    "payment_id": "payment_id",
    "status": "completed",
    "amount": 100.00,
    "currency": "USD",
    "reference": "order_reference"
}
```

## Supported Currencies

- USD (US Dollar)
- EUR (Euro)
- GBP (British Pound)
- CAD (Canadian Dollar)
- AUD (Australian Dollar)
- JPY (Japanese Yen)
- CHF (Swiss Franc)
- CNY (Chinese Yuan)
- SEK (Swedish Krona)
- NZD (New Zealand Dollar)

## Payment Flow

1. Customer selects FiatFlo as payment method
2. Plugin creates a payment link via FiatFlo API
3. Customer is redirected to FiatFlo payment page
4. Customer completes payment on FiatFlo
5. FiatFlo sends webhook notification to your site
6. Plugin processes the webhook and updates order status

## Troubleshooting

### Common Issues

1. **Payment link not created**: Check your API key configuration
2. **Webhook not received**: Verify webhook URL in FiatFlo dashboard
3. **Currency not supported**: Ensure the currency is in the supported list

### Logging

Payment logs are available in the admin panel under:
- Settings > Payment Methods > Payment Logs

## Support

For issues related to:
- **Plugin functionality**: Check the plugin logs and configuration
- **FiatFlo API**: Contact FiatFlo support
- **Botble CMS**: Refer to Botble documentation

## License

This plugin is released under the MIT License.
