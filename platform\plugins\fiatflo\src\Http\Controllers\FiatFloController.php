<?php

namespace Bo<PERSON>ble\FiatFlo\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Models\Payment;
use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Http\Request;

class FiatFloController extends BaseController
{
    public function webhook(Request $request, BaseHttpResponse $response)
    {
        try {
            // Log the webhook request for debugging
            PaymentHelper::log(
                FIATFLO_PAYMENT_METHOD_NAME,
                ['webhook_request' => $request->all()],
                ['headers' => $request->headers->all()]
            );

            $paymentId = $request->input('payment_id') ?? $request->input('id');
            $status = $request->input('status');
            $amount = $request->input('amount');
            $currency = $request->input('currency');
            $reference = $request->input('reference');

            if (! $paymentId) {
                PaymentHelper::log(
                    FIATFLO_PAYMENT_METHOD_NAME,
                    ['error' => 'Missing payment ID in webhook'],
                    ['request' => $request->all()]
                );

                return $response->setError()->setMessage('Missing payment ID');
            }

            // Find the payment record
            $payment = Payment::query()
                ->where('charge_id', $paymentId)
                ->orWhere('checkout_token', $reference)
                ->first();

            if (! $payment) {
                PaymentHelper::log(
                    FIATFLO_PAYMENT_METHOD_NAME,
                    ['error' => 'Payment not found', 'payment_id' => $paymentId, 'reference' => $reference]
                );

                return $response->setError()->setMessage('Payment not found');
            }

            // Update payment status based on FiatFlo status
            $paymentStatus = $this->mapFiatFloStatusToPaymentStatus($status);

            if ($paymentStatus && $payment->status !== PaymentStatusEnum::COMPLETED) {
                $payment->status = $paymentStatus;
                $payment->save();

                // Process the payment if completed
                if ($paymentStatus === PaymentStatusEnum::COMPLETED) {
                    do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
                        'amount' => $amount,
                        'currency' => $currency,
                        'charge_id' => $paymentId,
                        'payment_channel' => FIATFLO_PAYMENT_METHOD_NAME,
                        'status' => $paymentStatus,
                        'customer_id' => $payment->customer_id,
                        'customer_type' => $payment->customer_type,
                        'payment_type' => 'direct',
                        'order_id' => $payment->order_id,
                    ]);
                }

                PaymentHelper::log(
                    FIATFLO_PAYMENT_METHOD_NAME,
                    [
                        'message' => 'Payment status updated',
                        'payment_id' => $paymentId,
                        'status' => $paymentStatus->value,
                        'amount' => $amount,
                        'currency' => $currency,
                    ]
                );
            }

            return $response->setMessage('Webhook processed successfully');
        } catch (Exception $exception) {
            BaseHelper::logError($exception);

            PaymentHelper::log(
                FIATFLO_PAYMENT_METHOD_NAME,
                ['error' => $exception->getMessage()],
                ['trace' => $exception->getTraceAsString()]
            );

            return $response->setError()->setMessage('Error processing webhook: ' . $exception->getMessage());
        }
    }

    protected function mapFiatFloStatusToPaymentStatus(string $status): ?PaymentStatusEnum
    {
        return match (strtolower($status)) {
            'completed', 'success', 'paid' => PaymentStatusEnum::COMPLETED,
            'pending', 'processing' => PaymentStatusEnum::PENDING,
            'failed', 'error' => PaymentStatusEnum::FAILED,
            'cancelled', 'canceled' => PaymentStatusEnum::FAILED,
            'refunded' => PaymentStatusEnum::REFUNDED,
            default => null,
        };
    }
}
