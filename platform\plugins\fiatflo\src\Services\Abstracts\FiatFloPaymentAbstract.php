<?php

namespace Bo<PERSON>ble\FiatFlo\Services\Abstracts;

use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

abstract class FiatFloPaymentAbstract
{
    protected string $apiKey;
    protected string $methodType;
    protected float $amount;
    protected string $currency;
    protected string $chargeId;
    protected string $errorMessage = '';

    public function __construct()
    {
        $this->apiKey = get_payment_setting('api_key', FIATFLO_PAYMENT_METHOD_NAME);
        $this->methodType = get_payment_setting('method_type', FIATFLO_PAYMENT_METHOD_NAME, 'card');
    }

    public function getApiKey(): string
    {
        return $this->apiKey;
    }

    public function getMethodType(): string
    {
        return $this->methodType;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function getChargeId(): string
    {
        return $this->chargeId;
    }

    public function getErrorMessage(): string
    {
        return $this->errorMessage;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function setChargeId(string $chargeId): self
    {
        $this->chargeId = $chargeId;

        return $this;
    }

    public function setErrorMessage(string $errorMessage): self
    {
        $this->errorMessage = $errorMessage;

        return $this;
    }

    protected function setErrorMessageAndLogging(Exception $exception, int $line = 0): void
    {
        $this->errorMessage = $exception->getMessage();

        PaymentHelper::log(
            FIATFLO_PAYMENT_METHOD_NAME,
            [
                'error' => $this->errorMessage,
                'line' => $line,
                'file' => $exception->getFile(),
                'trace' => $exception->getTraceAsString(),
            ]
        );
    }

    public function execute(array $data): bool|string
    {
        try {
            $this->amount = $data['amount'];
            $this->currency = $data['currency'];

            return $this->makePayment($data);
        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 1);

            return false;
        }
    }

    abstract public function makePayment(array $data): bool|string;

    public function supportedCurrencyCodes(): array
    {
        return [
            'USD',
            'EUR',
            'GBP',
            'CAD',
            'AUD',
            'JPY',
            'CHF',
            'CNY',
            'SEK',
            'NZD',
        ];
    }

    abstract public function afterMakePayment(array $data): void;
}
