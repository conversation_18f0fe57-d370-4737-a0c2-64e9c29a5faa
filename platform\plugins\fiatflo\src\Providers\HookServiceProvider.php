<?php

namespace Bo<PERSON><PERSON>\FiatFlo\Providers;

use <PERSON><PERSON><PERSON>\Base\Facades\BaseHelper;
use <PERSON><PERSON>ble\FiatFlo\Forms\FiatFloPaymentMethodForm;
use Botble\FiatFlo\Services\Gateways\FiatFloPaymentService;
use Bo<PERSON><PERSON>\Payment\Enums\PaymentMethodEnum;
use Botble\Payment\Facades\PaymentMethods;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerFiatFloMethod'], 16, 2);
        add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithFiatFlo'], 16, 2);

        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 96);

        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == PaymentMethodEnum::class) {
                $values['FIATFLO'] = FIATFLO_PAYMENT_METHOD_NAME;
            }

            return $values;
        }, 21, 2);

        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == FIATFLO_PAYMENT_METHOD_NAME) {
                $value = 'FiatFlo';
            }

            return $value;
        }, 21, 2);

        add_filter(BASE_FILTER_ENUM_HTML, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == FIATFLO_PAYMENT_METHOD_NAME) {
                $value = PaymentMethods::getPaymentMethodImage(FIATFLO_PAYMENT_METHOD_NAME);
            }

            return $value;
        }, 21, 2);
    }

    public function addPaymentSettings(?string $settings): string
    {
        return $settings . FiatFloPaymentMethodForm::create()->renderForm();
    }

    public function registerFiatFloMethod(?string $html, array $data): ?string
    {
        PaymentMethods::method(FIATFLO_PAYMENT_METHOD_NAME, [
            'html' => view('plugins/fiatflo::methods', $data)->render(),
        ]);

        return $html;
    }

    public function checkoutWithFiatFlo(array $data, Request $request): array
    {
        if ($data['type'] !== FIATFLO_PAYMENT_METHOD_NAME) {
            return $data;
        }

        $paymentData = apply_filters(PAYMENT_FILTER_PAYMENT_DATA, [], $request);

        try {
            $fiatFloPaymentService = new FiatFloPaymentService();

            // Check if the service is valid to process checkout
            if (! $fiatFloPaymentService->isValidToProcessCheckout()) {
                $data['error'] = true;
                $data['message'] = __('FiatFlo payment method is not available.');

                return $data;
            }

            // Check supported currencies
            $supportedCurrencies = $fiatFloPaymentService->supportedCurrencyCodes();
            if (! in_array($paymentData['currency'], $supportedCurrencies)) {
                $data['error'] = true;
                $data['message'] = __(
                    ":name doesn't support :currency. List of currencies supported by :name: :currencies.",
                    [
                        'name' => 'FiatFlo',
                        'currency' => $paymentData['currency'],
                        'currencies' => implode(', ', $supportedCurrencies),
                    ]
                );

                return $data;
            }

            // Execute the payment
            $result = $fiatFloPaymentService->execute($paymentData);

            if ($fiatFloPaymentService->getErrorMessage()) {
                $data['error'] = true;
                $data['message'] = $fiatFloPaymentService->getErrorMessage();
            } elseif ($result) {
                // If result is a URL, redirect to FiatFlo payment page
                if (filter_var($result, FILTER_VALIDATE_URL)) {
                    $data['checkoutUrl'] = $result;
                    $data['charge_id'] = $fiatFloPaymentService->getChargeId();
                } else {
                    $data['charge_id'] = $result;
                }

                // Call after payment hook
                $fiatFloPaymentService->afterMakePayment($paymentData);
            } else {
                $data['error'] = true;
                $data['message'] = __('Payment failed! Please try again.');
            }
        } catch (Exception $exception) {
            BaseHelper::logError($exception);

            $data['error'] = true;
            $data['message'] = $exception->getMessage();
        }

        return $data;
    }
}
